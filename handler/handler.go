package handler

import (
	"context"
	"encoding/json"
	"futures-asset/util"
	"github.com/shopspring/decimal"
	"go.uber.org/dig"
	// 	"futures-asset/conf"
	// 	"futures-asset/handler/cron"
	// 	"futures-asset/handler/monitor"
	// 	"futures-asset/pkg/safely"
	// 	"futures-asset/pkg/setting"

	"futures-asset/internal/domain/usecase"

	"github.com/IBM/sarama"
	"github.com/sirupsen/logrus"

	futuresEnginePB "yt.com/backend/common.git/business/grpc/gen/futures/engine/v1"
)

type MessageProcessorParam struct {
	dig.In

	settleUseCase usecase.SettleUseCase
	assetUseCase  usecase.AssetUseCase
}

// MessageProcessor 消息处理器结构体
type MessageProcessor struct {
	settleUseCase usecase.SettleUseCase
	assetUseCase  usecase.AssetUseCase
}

// NewMessageProcessor 创建消息处理器实例
func NewMessageProcessor(param MessageProcessorParam) *MessageProcessor {
	return &MessageProcessor{
		settleUseCase: param.settleUseCase,
		assetUseCase:  param.assetUseCase,
	}
}

// func StartMonitors(ctx context.Context, wg *sync.WaitGroup) {
// 	go cron.StartCnyRate()
// 	go cron.StartRobotList()

// 	if conf.Conf.Biz.Monitor != 1 {
// 		return
// 	}

// 	contractSettings, err := setting.Service.GetAllPairSettingInfo()
// 	if err != nil {
// 		logrus.Panic("StartMonitors get all contract config err.")
// 		return
// 	}
// 	for contractCode := range contractSettings {
// 		if contractCode != "" {
// 			wg.Add(1)
// 			go monitor.SyncPosDb(ctx, wg, contractCode)
// 		}
// 	}

// 	for i := 0; i < 10; i++ {
// 		wg.Add(1)
// 		go monitor.SyncAssetDb(ctx, wg, strconv.Itoa(i))
// 	}

// 	wg.Add(8)
// 	go monitor.SyncBillAsset(ctx, wg)
// 	go monitor.SyncProfitLoss(ctx, wg)
// 	go monitor.SyncTrialAssetDb(ctx, wg)
// 	go monitor.BurstMonitor(ctx, wg)
// 	go monitor.SyncRobotBillAsset(ctx, wg)
// 	go monitor.SyncUserWinRate(ctx, wg)
// 	go monitor.SyncUserStatistics(ctx, wg)
// 	go monitor.SyncOptionBill(ctx, wg)

// 	go monitor.ContractMqPublishAsset()
// 	go monitor.PublishTrade()
// 	go cron.StartFundRateData()
// 	go cron.StartProfitStaticData()
// 	go cron.StartSyncOptionProfitData()
// 	go cron.StartTrialAssetData()
// 	go cron.StartPlatPosPush()
// 	safely.Run(cron.StartCleanData)

// 	go cron.StartStaticPlatPos(contractSettings)
// 	go monitor.SubscribePair(ctx, wg)

// 	go cron.StartUpdateAccountPrincipal()
// }

// MessageQueueHandler 根据topic名称解析不同类型的消息数据
func (mp *MessageProcessor) MessageQueueHandler(
	session sarama.ConsumerGroupSession,
	claim sarama.ConsumerGroupClaim,
	msg *sarama.ConsumerMessage,
) {
	defer func() {
		session.MarkMessage(msg, "")
		if err := recover(); err != nil {
			logrus.WithFields(logrus.Fields{
				"topic":     msg.Topic,
				"partition": msg.Partition,
				"offset":    msg.Offset,
				"error":     err,
			}).Error("MessageQueueHandler panic recovered")
		}
	}()

	logrus.WithFields(logrus.Fields{
		"topic":     msg.Topic,
		"partition": msg.Partition,
		"offset":    msg.Offset,
		"key":       string(msg.Key),
	}).Info("MessageQueueHandler received message")

	// 根据topic名称确定消息类型并解析
	var messageData interface{}
	var err error

	switch msg.Topic {
	case "futures-settle-trade-account":
		// 账户结算消息
		var data futuresEnginePB.AccountSettleEngine
		err = json.Unmarshal(msg.Value, &data)
		messageData = &data

	case "futures-settle-order-cancel":
		// 订单取消消息
		var data futuresEnginePB.Order
		err = json.Unmarshal(msg.Value, &data)
		messageData = &data

	default:
		// 未知的topic类型
		logrus.WithFields(logrus.Fields{
			"topic":     msg.Topic,
			"partition": msg.Partition,
			"offset":    msg.Offset,
			"message":   string(msg.Value),
		}).Error("MessageQueueHandler unknown topic type")
		return
	}

	if err != nil {
		logrus.WithFields(logrus.Fields{
			"topic":     msg.Topic,
			"partition": msg.Partition,
			"offset":    msg.Offset,
			"error":     err,
			"message":   string(msg.Value),
		}).Error("MessageQueueHandler failed to unmarshal message")
		return
	}

	// 处理解析后的消息数据
	mp.processMessageData(msg.Topic, messageData)

	logrus.WithFields(logrus.Fields{
		"topic":     msg.Topic,
		"partition": msg.Partition,
		"offset":    msg.Offset,
	}).Info("MessageQueueHandler processed successfully")
}

// processMessageData 处理不同类型的消息数据
func (mp *MessageProcessor) processMessageData(topic string, data interface{}) {
	switch topic {
	case "futures-settle-trade-account":
		if accountData, ok := data.(*futuresEnginePB.AccountSettleEngine); ok {
			logrus.WithFields(logrus.Fields{
				"topic": topic,
				"data":  accountData,
			}).Info("tp0803 start process account settle message")

			// 调用结算业务处理
			if mp.settleUseCase != nil {
				err := mp.settleUseCase.ProcessAccountSettle(accountData)
				if err != nil {
					logrus.WithFields(logrus.Fields{
						"data":  accountData,
						"error": err,
					}).Error("tp0803 fail to process account settle message")
				} else {
					logrus.WithFields(logrus.Fields{
						"uid":     accountData.Order.UserId,
						"tradeid": accountData.TradeId,
						"orderid": accountData.Order.OrderId,
					}).Info("tp0803 account settle message processed successfully")
				}
			} else {
				logrus.WithFields(logrus.Fields{
					"topic": topic,
				}).Error("SettleUseCase is not initialized")
			}
		}

	case "futures-settle-order-cancel":
		if orderData, ok := data.(*futuresEnginePB.Order); ok {
			logrus.WithFields(logrus.Fields{
				"topic": topic,
				"data":  orderData,
			}).Info("Processing order cancel message")

			// 订单取消逻辑
			if mp.assetUseCase == nil {
				logrus.WithFields(logrus.Fields{"topic": topic}).
					Error("OrderUseCase is not initialized")
				return
			}

			fromString, err := decimal.NewFromString(orderData.Amount)
			if err != nil {
				logrus.WithFields(logrus.Fields{"topic": topic}).
					Error("OrderUseCase is not initialized")
				return
			}

			contractCode := util.ContractCode(orderData.Symbol.Base, orderData.Symbol.Quote)

			req := &usecase.BatchUnlockParam{
				UID:          orderData.UserId,
				UserType:     orderData.UserType,
				Symbol:       contractCode,
				Currency:     orderData.Symbol.Base,
				MarginMode:   orderData.MarginMode,
				PositionMode: orderData.PositionMode,
				Orders: []*usecase.UnLockParam{
					{
						UID:          orderData.UserId,
						UserType:     orderData.UserType,
						OrderId:      orderData.OrderId,
						Symbol:       orderData.Symbol.ToString(),
						Currency:     orderData.Symbol.Quote,
						Amount:       fromString,
						MarginMode:   orderData.MarginMode,
						PositionMode: orderData.PositionMode,
						AwardIds:     orderData.AwardIds,
						TrialIsEnd:   len(orderData.AwardIds) != 0,
					},
				},
			}
			_, err = mp.assetUseCase.UnLockAsset(context.Background(), req)
			if err != nil {
				logrus.WithFields(logrus.Fields{"data": orderData, "error": err}).
					Error("tp0803 fail to process UnlockOrder message")
				return
			}

			logrus.WithFields(logrus.Fields{"order": orderData}).
				Info("tp0803 UnlockOrder message processed successfully")
		}

	default:
		logrus.WithFields(logrus.Fields{
			"topic": topic,
		}).Warn("No specific handler for this topic")
	}
}
