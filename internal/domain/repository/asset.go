package repository

import (
	"context"
	"encoding/json"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/util"
	"sort"
	"strings"
	commonpb "yt.com/backend/common.git/business/grpc/gen/ws/v1"

	"github.com/shopspring/decimal"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

type AssetRepository interface {
	UpsertAsset(ctx context.Context, data entity.Wallet) error
	UpdateAsset(ctx context.Context, data entity.Wallet, changeType commonpb.FundingChangeType) error
	GetBatchUserAsset(ctx context.Context, req BalanceReq) ([]entity.Wallet, error)
	SumUserTotalAsset(ctx context.Context, req TotalAssetReq) ([]entity.Wallet, error)
	GetAvailableBase(ctx context.Context, asset *AssetSwap, marginMode futuresassetpb.MarginMode, currency string) (decimal.Decimal, error)

	CalcTrialMargin(ctx context.Context, asset *AssetSwap, currency string, posMargin decimal.Decimal, pos PosSwap) (PosSwap, []*entity.TrialAsset)
	TotalJoinBalance(ctx context.Context, asset *AssetSwap) (balance decimal.Decimal, err error)
	UpdateUserHoldPos(ctx context.Context, asset *AssetSwap, long, short, both PosSwap, userType int32, trial bool) error

	CanTransfer(ctx context.Context, asset *AssetSwap, currency string) (decimal.Decimal, error)

	// CrossMarginBalance 返回全仓保证金余额 (账户余额+全仓未实现盈亏-逐仓仓位保证金)
	CrossMarginBalance(ctx context.Context, asset *AssetSwap, uid, currency string) (decimal.Decimal, error)
	// FrozenTotal 返回冻结金额
	FrozenTotal(ctx context.Context, asset *AssetSwap, area string, isTrials ...bool) decimal.Decimal
	// GetMarginBalanceAndMaintainMargin 获取仓位的保证金余额及维持保证金
	GetMarginBalanceAndMaintainMargin(ctx context.Context, asset *AssetSwap, pos PosSwap) (decimal.Decimal, decimal.Decimal, error)
	// OptionsMarginBalance 返回期权全仓保证金余额 (账户余额+全仓未实现盈亏-逐仓仓位保证金)
	OptionsMarginBalance(ctx context.Context, asset *AssetSwap, currency string) (decimal.Decimal, error)
	// OtherCrossMaintainMargin 其他全仓币对的维持保证金总和
	OtherCrossMaintainMargin(ctx context.Context, asset *AssetSwap, code string) decimal.Decimal
	// OtherCrossUnreal 其他全仓币对的未实现盈亏总和
	OtherCrossUnreal(ctx context.Context, asset *AssetSwap, code string) decimal.Decimal
	// TotalCrossMaintainMargin 获取全部全仓的维持保证金总和
	TotalCrossMaintainMargin(ctx context.Context, asset *AssetSwap, code string) (decimal.Decimal, decimal.Decimal, map[string]bool, map[string]LevelFilter, error)

	// CrossUnrealTotal 全部全仓的未实现盈亏 单币保证金不折u按照交易区获取 联合保证金折u
	CrossUnrealTotal(ctx context.Context, asset *AssetSwap, area string) decimal.Decimal
	// HoldCostTotalIsolated 全部逐仓的持仓成本 只有单币保证金有逐仓 所以必须带交易区
	HoldCostTotalIsolated(ctx context.Context, asset *AssetSwap, area string, isRemoveTrialPos bool) decimal.Decimal
	// HoldCostTotalCross 全部全仓的持仓成本 单币保证金不折u按照交易区获取 联合保证金折u
	HoldCostTotalCross(ctx context.Context, asset *AssetSwap, area string) decimal.Decimal
	// HoldCostTotal 所有币对的持仓成本 单币保证金不折u按照交易区获取 联合保证金折u
	HoldCostTotal(ctx context.Context, asset *AssetSwap, area string) decimal.Decimal
	// HoldCostTrialTotal 所有币对的持仓成本 单币保证金不折u按照交易区获取 联合保证金折u
	HoldCostTrialTotal(ctx context.Context) decimal.Decimal
	// CrossTrialUnrealTotal 全部全仓的未实现盈亏 单币保证金不折u按照交易区获取 联合保证金折u
	CrossTrialUnrealTotal(ctx context.Context) decimal.Decimal
}

type BatchAssetSwap struct {
	UID          string          `json:"uid"`           // 用户ID
	Currency     string          `json:"currency"`      // 资产币种
	TotalBalance decimal.Decimal `json:"total_balance"` // 账户资产总余额
}

type (
	ReqUserAssetAndPos struct {
		Asset   AssetData `json:"asset"`
		PosList []PosData `json:"pos_list"`
	}
	AssetData struct {
		UID           string                     `json:"uid"`            // 用户id
		Balance       map[string]decimal.Decimal `json:"balance"`        // 账户余额
		Available     decimal.Decimal            `json:"available"`      // 可用
		ProfitUnreal  decimal.Decimal            `json:"profit_unreal"`  // 总未实现盈亏
		MarginBalance decimal.Decimal            `json:"margin_balance"` // 保证金余额
		ProfitRate    decimal.Decimal            `json:"profit_rate"`    // 盈亏比例
	}
	PosData struct {
		Symbol       string          `json:"symbol"`         // 合约代码
		Currency     string          `json:"currency"`       // 币种
		UID          string          `json:"uid"`            // 用户ID
		Leverage     int             `json:"leverage"`       // 杠杆倍数
		Pos          decimal.Decimal `json:"pos"`            // 仓位数
		OpenPriceAvg decimal.Decimal `json:"open_price_avg"` // 开仓均价
		MarkPrice    decimal.Decimal `json:"mark_price"`     // 标记价格
		Liquidation  decimal.Decimal `json:"liquidation"`    // 预估强评价
		MarginMode   int32           `json:"margin_mode"`    // 仓位类型 1:全仓 2:逐仓
		Margin       decimal.Decimal `json:"margin"`         // 仓位保证金
		ProfitUnreal decimal.Decimal `json:"profit_unreal"`  // 未实现盈亏(回报率)
		RiskRate     decimal.Decimal `json:"risk_rate"`      // 风险率
		PosSide      int32           `json:"pos_side"`       // 仓位方向 (0:单向持仓仓位信息 1:多仓 2:空仓)
		AwardIds     []string        `json:"award_ids"`      // 奖励操作id
	}
)

type (
	ResSumUserTotalAssetItem struct {
		Currency string          `json:"currency"` // 资产币种
		Balance  decimal.Decimal `json:"balance"`  // 账户余额
	}
	ResSumUserTotalAsset struct {
		UserAsset  []ResSumUserTotalAssetItem `json:"user_asset"`  // 用户资产
		RobotAsset []ResSumUserTotalAssetItem `json:"robot_asset"` // 机器人资产
	}
)

type ResTotalBalance struct {
	Currency     string          `json:"currency"`
	TotalTotal   decimal.Decimal `json:"total_total"`   // 币种折Currency 的余额
	TotalProfit  decimal.Decimal `json:"total_profit"`  // 累计盈亏折 U
	TrialBalance decimal.Decimal `json:"trial_balance"` // todo 需要等体验金需求确认之后再开发
}

type ResAssetDetail struct {
	AssetMode futuresassetpb.AssetMode   `json:"asset_mode"` // 保证金模式 1.单币保证金 2.联合保证金
	UID       string                     `json:"uid"`        // 用户id
	Frozen    map[string]decimal.Decimal `json:"frozen"`     // 冻结
	AssetList []AssetDetail              `json:"asset_list"` // 用户资产信息
	PosList   []PosSwap                  `json:"pos_list"`   // 用户持仓列表
}

type AssetDetail struct {
	Currency     string          `json:"currency"` // 币种
	TrialBalance decimal.Decimal `json:"trial_balance"`
	Balance      decimal.Decimal `json:"balance"` // 余额
}

type BalanceReq struct {
	UIDs       []string
	Currencies []string
}

type TotalAssetReq struct {
	UIDs      []string
	UIDsNotIn []string
}

// Leverage 杠杆倍数实体
type Leverage struct {
	Symbol     string                    `json:"symbol"`      // 币对
	MarginMode futuresassetpb.MarginMode `json:"margin_mode"` // 仓位模式 1 全仓 2 逐仓
	Leverage   int                       `json:"leverage"`    // 全仓 杠杆倍数
	LLeverage  int                       `json:"l_leverage"`  // 逐仓 多仓杠杆倍数
	SLeverage  int                       `json:"s_leverage"`  // 逐仓 空仓杠杆倍数
	BLeverage  int                       `json:"b_leverage"`  // 单向持仓 逐仓杠杆倍数
}

func (t *Leverage) GetLeverage(positionMode futuresassetpb.PositionMode, marginMode futuresassetpb.MarginMode, posSide futuresassetpb.PosSide) int {
	if positionMode == futuresassetpb.PositionMode_POSITION_MODE_ONE_WAY { // 如果是单向持仓
		return t.BLeverage
	}
	if marginMode == futuresassetpb.MarginMode_MARGIN_MODE_CROSS { // 如果是全仓
		return t.Leverage
	}
	// 开仓买入，跟平仓卖出，都是多仓
	// isLong := (offset == domain.Open && side == domain.Buy) || (offset == domain.Close && side == domain.Sell)
	if posSide == futuresassetpb.PosSide_POS_SIDE_LONG {
		return t.LLeverage
	}

	return t.SLeverage
}

// AssetSwap Frozen冻结保证金支持单向持仓 单向持仓：币对-买卖 双向持仓：币对-多空
type AssetSwap struct {
	UID           string                      `json:"uid"`             // 账户余额
	Balance       BalanceAsset                `json:"balance"`         // 账户余额
	TrialBalance  BalanceAsset                `json:"trial_balance"`   // 体验金账户余额
	TrialConsume  BalanceAsset                `json:"trial_consume"`   // 体验金账户消耗
	TrialLoss     BalanceAsset                `json:"trial_loss"`      // 体验金账户亏损
	TrialRecovery BalanceAsset                `json:"trial_recovery"`  // 体验金账户回收
	Available     decimal.Decimal             `json:"available"`       // 可用 (// 可用 (保证金余额8-已用8-冻结8))
	Leverage      []*Leverage                 `json:"leverage"`        // 杠杆倍数
	Frozen        map[string]decimal.Decimal  `json:"frozen"`          // 冻结的保证金 混合保证金冻结的是usd
	ShortPos      PosSwap                     `json:"short_pos"`       // 空仓
	LongPos       PosSwap                     `json:"long_pos"`        // 多仓
	BothPos       PosSwap                     `json:"both_pos"`        // 单向持仓
	TrialShortPos PosSwap                     `json:"trial_short_pos"` // 体验金空仓
	TrialLongPos  PosSwap                     `json:"trial_long_pos"`  // 体验金多仓
	TrialBothPos  PosSwap                     `json:"trial_both_pos"`  // 体验金单向持仓
	TrialDetail   TrialTimeList               `json:"trial_detail"`    // 体验金详细记录
	PositionMode  futuresassetpb.PositionMode `json:"position_mode"`   // 持仓模式
	AssetMode     futuresassetpb.AssetMode    `json:"asset_mode"`      // 保证金模式

	CrossList    map[string][]PosSwap
	IsolatedList map[string][]PosSwap
}

type BalanceAsset map[string]decimal.Decimal

// Add 添加余额
func (b *BalanceAsset) Add(currency string, amount decimal.Decimal) error {
	if b == nil {
		*b = make(map[string]decimal.Decimal)
	}
	currency = strings.ToUpper(currency)
	val, ok := (*b)[currency]
	if !ok {
		(*b)[currency] = amount
	} else {
		(*b)[currency] = val.Add(amount)
	}

	return nil
}

// Get 获取余额
func (b *BalanceAsset) Get(currency string) (amount decimal.Decimal) {
	currency = strings.ToUpper(currency)
	amount, _ = (*b)[currency]

	return
}

func (as *AssetSwap) GetLeverage(contractCode string) Leverage {
	for _, leverage := range as.Leverage {
		if strings.EqualFold(leverage.Symbol, contractCode) {
			return *leverage
		}
	}

	return Leverage{
		Symbol:     contractCode,
		MarginMode: futuresassetpb.MarginMode_MARGIN_MODE_CROSS,
		Leverage:   10, // 全仓 杠杆倍数(单向, 双向持仓共用)
		LLeverage:  10, // 逐仓 多仓杠杆倍数
		SLeverage:  10, // 逐仓 空仓杠杆倍数
		BLeverage:  10, // 单向持仓 逐仓杠杆倍数
	}
}

// GetPos 获取持仓
func (as *AssetSwap) GetPos(side futuresassetpb.PosSide, trial bool) PosSwap {
	if side == futuresassetpb.PosSide_POS_SIDE_LONG {
		if trial {
			return as.TrialLongPos
		}
		return as.LongPos
	} else if side == futuresassetpb.PosSide_POS_SIDE_SHORT {
		if trial {
			return as.TrialShortPos
		}
		return as.ShortPos
	}
	if trial {
		return as.TrialBothPos
	}

	return as.BothPos
}

// SetPos 设置持仓
func (as *AssetSwap) SetPos(side int32, pos PosSwap, trial bool) {
	if side == domain.LongPos {
		if trial {
			as.TrialLongPos = pos
		} else {
			as.LongPos = pos
		}
	} else if side == domain.ShortPos {
		if trial {
			as.TrialShortPos = pos
		} else {
			as.ShortPos = pos
		}
	} else {
		if trial {
			as.TrialBothPos = pos
		} else {
			as.BothPos = pos
		}
	}
}

// CBalance 根据币种获取币种剩余数量
func (as *AssetSwap) CBalance(currency string) decimal.Decimal {
	if v, ok := as.Balance[strings.ToUpper(currency)]; ok {
		return v
	}

	return decimal.Zero
}

// TrialCBalance 根据币种获取体验金币种剩余数量
func (as *AssetSwap) TrialCBalance(currency string) decimal.Decimal {
	// TODO: 暂时返回空，不影响老的代码
	return decimal.Zero
}

// TrialCConsume 根据币种获取体验金币种消耗数量
func (as *AssetSwap) TrialCConsume(currency string) decimal.Decimal {
	if v, ok := as.TrialConsume[strings.ToUpper(currency)]; ok {
		return v
	}

	return decimal.Zero
}

// TotalJoinTrialBalance 获取用户当前所有体验金余额
func (as *AssetSwap) TotalJoinTrialBalance() (trialBalance decimal.Decimal, err error) {
	trialBalance = decimal.Zero

	return
}

// AddBalance 修改币种余额
func (as *AssetSwap) AddBalance(currency string, amount decimal.Decimal) {
	if _, ok := as.Balance[strings.ToUpper(currency)]; ok {
		as.Balance[strings.ToUpper(currency)] = as.Balance[strings.ToUpper(currency)].Add(amount)
	} else {
		as.Balance[strings.ToUpper(currency)] = amount
	}
}

// AddTrialBalance 修改体验金币种余额
func (as *AssetSwap) AddTrialBalance(currency string, amount decimal.Decimal) {
	if _, ok := as.TrialBalance[strings.ToUpper(currency)]; ok {
		as.TrialBalance[strings.ToUpper(currency)] = as.TrialBalance[strings.ToUpper(currency)].Add(amount)
	} else {
		as.TrialBalance[strings.ToUpper(currency)] = amount
	}
}

// AddTrialConsume 修改体验金消耗币种余额
func (as *AssetSwap) AddTrialConsume(currency string, amount decimal.Decimal) {
	if _, ok := as.TrialConsume[strings.ToUpper(currency)]; ok {
		as.TrialConsume[strings.ToUpper(currency)] = as.TrialConsume[strings.ToUpper(currency)].Add(amount)
	} else {
		as.TrialConsume[strings.ToUpper(currency)] = amount
	}
}

// ConsumeTrialBalance 修改体验金币种余额
func (as *AssetSwap) ConsumeTrialBalance(currency string, amount decimal.Decimal) []*entity.TrialAsset {
	trialBillList := make([]*entity.TrialAsset, 0)
	if len(as.TrialDetail) > 0 {
		sort.Sort(as.TrialDetail)
		discountAmount := amount
		for _, trialInfo := range as.TrialDetail {
			// 暂时不做失效时间判断，因为读取时候已经做过，如果真的此时失效，读取时候其实没失效
			if discountAmount.GreaterThan(decimal.Zero) && strings.ToUpper(currency) == trialInfo.Currency {
				if trialInfo.AmountUsed.LessThan(trialInfo.AwardAmount) {
					ableAmount := trialInfo.AwardAmount.Sub(trialInfo.AmountUsed)
					if ableAmount.GreaterThanOrEqual(discountAmount) {
						trialInfo.AmountUsed = trialInfo.AmountUsed.Add(discountAmount)
						as.AddTrialBalance(currency, discountAmount.Neg())
						as.AddTrialConsume(currency, discountAmount)
						trialInfo.RecycleAmount = trialInfo.AwardAmount.Sub(trialInfo.AmountUsed)
						trialBillList = append(trialBillList, trialInfo)
						discountAmount = decimal.Zero
						break
					} else {
						trialInfo.AmountUsed = trialInfo.AmountUsed.Add(ableAmount)
						as.AddTrialBalance(currency, ableAmount.Neg())
						as.AddTrialConsume(currency, ableAmount)
						trialInfo.RecycleAmount = trialInfo.AwardAmount.Sub(trialInfo.AmountUsed)
						trialBillList = append(trialBillList, trialInfo)
						discountAmount = discountAmount.Sub(ableAmount)
					}
				}
			}
		}
	}
	return trialBillList
}

func (as *AssetSwap) BothFrozen(contractCode string, pos PosSwap, isTrial bool) decimal.Decimal {
	fn := util.FrozenKey
	if isTrial {
		fn = util.TrialFrozenKey
	}

	buyFrozen, sellFrozen := decimal.Zero, decimal.Zero
	mapKey := fn(contractCode)
	if f, ok := as.Frozen[mapKey]; ok {
		buyFrozen = f
	}
	mapKey = fn(contractCode)
	if f, ok := as.Frozen[mapKey]; ok {
		sellFrozen = f
	}
	if pos.Pos.IsZero() {
		return buyFrozen.Add(sellFrozen)
	} else if pos.Pos.IsPositive() {
		f := sellFrozen.Sub(pos.OpenHoldCost().Mul(decimal.NewFromInt(2)))
		if f.IsPositive() {
			return buyFrozen.Add(f)
		}
		return buyFrozen
	} else if pos.Pos.IsNegative() {
		f := buyFrozen.Sub(pos.OpenHoldCost().Mul(decimal.NewFromInt(2)))
		if f.IsPositive() {
			return sellFrozen.Add(f)
		}
		return sellFrozen
	}

	return decimal.Zero
}

// GetFrozenByCode 获取某币对下冻结保证金
func (as *AssetSwap) GetFrozenByCode(contractCode string, isTrials ...bool) decimal.Decimal {
	isTrial := len(isTrials) > 0 && isTrials[0]
	fn := util.FrozenKey
	if isTrial {
		fn = util.TrialFrozenKey
	}

	totalFrozen := decimal.Zero
	var mapKey string
	if as.PositionMode == domain.HoldModeHedge {
		mapKey = fn(contractCode)
		if f, ok := as.Frozen[mapKey]; ok {
			totalFrozen = totalFrozen.Add(f)
		}
		mapKey = fn(contractCode)
		if f, ok := as.Frozen[mapKey]; ok {
			totalFrozen = totalFrozen.Add(f)
		}
	} else if as.PositionMode == domain.HoldModeBoth {
		return as.BothFrozen(contractCode, as.BothPos, isTrial)
	}

	return totalFrozen
}

// HasFrozen 该用户是否有冻结 contractCode=” 表示看所有币对， contractCode!=”只看一个币对
func (as *AssetSwap) HasFrozen(contractCode string) bool {
	for k, v := range as.Frozen {
		if len(contractCode) > 0 && !strings.HasPrefix(k, contractCode) {
			continue
		}
		if v.IsPositive() {
			return true
		}
	}

	return false
}

// IncrFrozen 增加冻结 side 双向持仓1多2空 单向持仓1买2卖
func (as *AssetSwap) IncrFrozen(contractCode string, frozen decimal.Decimal, isTrials ...bool) {
	isTrial := len(isTrials) > 0 && isTrials[0]
	fn := util.FrozenKey
	if isTrial {
		fn = util.TrialFrozenKey
	}

	if as.Frozen == nil {
		as.Frozen = make(map[string]decimal.Decimal)
	}
	mapKey := fn(contractCode)
	f, ok := as.Frozen[mapKey]
	if ok {
		as.Frozen[mapKey] = f.Add(frozen)
	} else {
		as.Frozen[mapKey] = frozen
	}

	return
}

// DecrFrozen 扣减冻结
func (as *AssetSwap) DecrFrozen(contractCode string, frozen decimal.Decimal, isTrials ...bool) {
	isTrial := len(isTrials) > 0 && isTrials[0]
	fn := util.FrozenKey
	if isTrial {
		fn = util.TrialFrozenKey
	}

	if as.Frozen == nil {
		as.Frozen = make(map[string]decimal.Decimal)
	}

	mapKey := fn(contractCode)
	f, ok := as.Frozen[mapKey]
	if ok {
		f = f.Sub(frozen)
		if f.IsNegative() {
			f = decimal.Zero
		}
		as.Frozen[mapKey] = f
	}

	return
}

// SetPosLeverAge 调整杠杆倍数时 设置仓位的杠杆倍数
func (as *AssetSwap) SetPosLeverAge(leverAge int) {
	if !as.LongPos.Pos.IsZero() {
		as.LongPos.Leverage = leverAge
	}
	if !as.ShortPos.Pos.IsZero() {
		as.ShortPos.Leverage = leverAge
	}
	if !as.BothPos.Pos.IsZero() {
		as.BothPos.Leverage = leverAge
	}
	return
}

// UserPos 获取用户全仓及逐仓仓位
func (as *AssetSwap) UserPos(getAll bool, area string, symbol string, markPrice decimal.Decimal) ([]PosSwap, []PosSwap, error) {
	crossPos, isolatedPos := make([]PosSwap, 0), make([]PosSwap, 0)

	for contractCode, posList := range as.CrossList {
		if !getAll {
			if len(area) > 0 {
				if strings.Index(strings.ToUpper(contractCode), strings.ToUpper(area)) <= 0 {
					continue
				}
			} else {
				if !strings.Contains(contractCode, symbol) {
					continue
				}
			}
		}
		for _, v := range posList {
			if v.Pos.IsZero() {
				continue
			}
			v.PosValue = v.CalcPosValue(markPrice)
			v.OpenTime = v.OpenTime / 1e9
			crossPos = append(crossPos, v)
		}
	}
	for contractCode, posList := range as.IsolatedList {
		if !getAll && !strings.Contains(contractCode, symbol) {
			continue
		}
		for _, v := range posList {
			if v.Pos.IsZero() {
				continue
			}
			v.PosValue = v.CalcPosValue(markPrice)
			v.OpenTime = v.OpenTime / 1e9
			isolatedPos = append(isolatedPos, v)
		}
	}

	return crossPos, isolatedPos, nil
}

// MarshalBinary implement encoding.MarshalBinary for redis
func (b *BalanceAsset) MarshalBinary() ([]byte, error) {
	return json.Marshal(b)
}

// UnmarshalBinary implement encoding.BinaryUnmarshaler for redis
func (b *BalanceAsset) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, &b)
}

// --------------------------上面是调整后的最终版公式---------
