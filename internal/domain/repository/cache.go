package repository

import (
	"context"

	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

type CacheRepository interface {
	InitUser(ctx context.Context, uid string, leverage []*Leverage) error

	GetAllCacheKey(ctx context.Context) ([]string, error)
	Load(ctx context.Context, uid, pair, currency string) (*AssetSwap, error)

	UpdateBalance(ctx context.Context, asset *AssetSwap) error
	UpdatePos(ctx context.Context, pair string, asset *AssetSwap, trial bool) error
	UpdateLongPos(ctx context.Context, pair string, asset *AssetSwap) error
	UpdateShortPos(ctx context.Context, pair string, asset *AssetSwap) error
	UpdateBothPos(ctx context.Context, pair string, asset *AssetSwap) error
	UpdatePosAndAsset(ctx context.Context, asset *AssetSwap) error
	SetPos(ctx context.Context, side futuresassetpb.PosSide, pos PosSwap, trial bool) error
	SetLongPos(ctx context.Context, pos PosSwap) error
	SetShortPos(ctx context.Context, pos PosSwap) error
	SetBothPos(ctx context.Context, pos PosSwap) error
	UpdateAnyPos(ctx context.Context, pos PosSwap) error
	SetLeverage(ctx context.Context, uid string, leverage map[string]*Leverage) error

	GetUserAssetMode(ctx context.Context, uid string) (futuresassetpb.AssetMode, error)
	UpdateAssetMode(ctx context.Context, uid string, mode futuresassetpb.AssetMode) error
	GetUserPositions(ctx context.Context, uid, pair string) (map[string][]PosSwap, map[string][]PosSwap, error)
	UpdateLeverage(ctx context.Context, uid string, leverage []*Leverage) error
	UpdatePositionMode(ctx context.Context, uid string, mode futuresassetpb.PositionMode) error
	FixInitUser(ctx context.Context, uid string, leverage map[string]*Leverage) error
}
