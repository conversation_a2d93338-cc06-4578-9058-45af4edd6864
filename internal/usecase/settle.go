package usecase

import (
	"context"
	"fmt"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"
	"futures-asset/internal/utils"
	"futures-asset/util"
	"time"

	commonpb "yt.com/backend/common.git/business/grpc/gen/ws/v1"

	"github.com/go-redsync/redsync/v4"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
	futuresEnginePB "yt.com/backend/common.git/business/grpc/gen/futures/engine/v1"
	futuresPB "yt.com/backend/common.git/business/grpc/gen/futures/v1"
)

// SettleUseCase 结算用例实现
type SettleUseCase struct {
	cacheRepo  repository.CacheRepository
	priceRepo  repository.PriceRepository
	positionUC usecase.PositionUseCase
	producerUC usecase.ProducerUseCase
	rs         *redsync.Redsync
}

// SettleUseCaseParam 结算用例参数
type SettleUseCaseParam struct {
	dig.In
	CacheRepo  repository.CacheRepository
	PriceRepo  repository.PriceRepository
	PositionUC usecase.PositionUseCase
	producerUC usecase.ProducerUseCase
	RS         *redsync.Redsync `name:"rs"`
}

// NewSettleUseCase 创建结算用例实例
func NewSettleUseCase(param SettleUseCaseParam) *SettleUseCase {
	return &SettleUseCase{
		cacheRepo:  param.CacheRepo,
		priceRepo:  param.PriceRepo,
		positionUC: param.PositionUC,
		producerUC: param.producerUC,
		rs:         param.RS,
	}
}

// ProcessAccountSettle 处理账户结算业务
// 参考Trade.Trade()和Trade.Process()函数实现，但只处理单个账户参数
func (use *SettleUseCase) ProcessAccountSettle(accountSettle *futuresEnginePB.AccountSettleEngine) error {
	accountSettleParam := utils.AccountSettleParam{AccountSettle: accountSettle}
	uid := accountSettleParam.GetUID()
	base, quote := accountSettleParam.GetBaseQuote()

	if uid == "" || base == "" || quote == "" {
		return fmt.Errorf("invalid account settle data: uid=%s, base=%s, quote=%s", uid, base, quote)
	}

	// 锁定用户资产
	userMutex := use.rs.NewMutex(domain.MutexSwapPosLock+uid, redsync.WithExpiry(30*time.Second))
	if err := userMutex.Lock(); err != nil {
		return fmt.Errorf("failed to lock user asset: err=%v, uid=%s", err, uid)
	}
	defer func() {
		unlockOk, unlockErr := userMutex.Unlock()
		if unlockErr != nil || !unlockOk {
			logrus.Errorf("failed to unlock user asset: ok=%v, err=%v, uid=%s", unlockOk, unlockErr, uid)
		}
	}()

	// 加载用户资产
	ctx := context.Background()
	contractCode := util.ContractCode(base, quote)
	userAsset, err := use.cacheRepo.Load(ctx, uid, contractCode, quote)
	if err != nil {
		return fmt.Errorf("failed to load user asset: err=%v, uid=%s", err, uid)
	}
	// 打印用户资产信息到日志
	logrus.WithFields(logrus.Fields{
		"tradeid":      accountSettleParam.GetTradeId(),
		"uid":          uid,
		"contractCode": contractCode,
		"PositionMode": userAsset.PositionMode.String(),
		"AssetMode":    userAsset.AssetMode.String(),
		"Balance":      userAsset.Balance,
		"LongPos":      userAsset.LongPos,
		"ShortPos":     userAsset.ShortPos,
		"BothPos":      userAsset.BothPos,
	}).Info("tp0803 user current asset")

	// 处理结算业务逻辑
	changeLog := make([]string, 0)
	err = use.processAccountSettlement(ctx, userAsset, accountSettleParam, &changeLog)
	if err != nil {
		return fmt.Errorf("process settlement error: err=%v, uid=%s", err, uid)
	}

	// 打印变更后用户资产信息到日志
	logrus.WithFields(logrus.Fields{
		"tradeid":      accountSettleParam.GetTradeId(),
		"orderid":      accountSettleParam.GetOrderID(),
		"uid":          uid,
		"contractCode": contractCode,
		"PositionMode": userAsset.PositionMode.String(),
		"AssetMode":    userAsset.AssetMode.String(),
		"Balance":      userAsset.Balance,
		"LongPos":      userAsset.LongPos,
		"ShortPos":     userAsset.ShortPos,
		"BothPos":      userAsset.BothPos,
		"changeLog":    changeLog,
	}).Info("tp0803 user asset change summary")

	// 保存落库更新后的资产
	err = use.saveUserAsset(ctx, userAsset, contractCode)
	if err != nil {
		return fmt.Errorf("save user asset error: err=%v, uid=%s", err, uid)
	}

	return nil
}

// processAccountSettlement 处理账户结算的核心业务逻辑
// 参考原来的Trade.Process()函数实现，但只处理单个账户的结算
func (use *SettleUseCase) processAccountSettlement(ctx context.Context, userAsset *repository.AssetSwap, accountSettleParam utils.AccountSettleParam, changelog *[]string) error {

	if userAsset == nil {
		return fmt.Errorf("user asset is nil")
	}

	openclose := "close"
	if accountSettleParam.IsOpenPosition() {
		openclose = "open"
	}

	logrus.WithFields(logrus.Fields{
		"tradeid":       accountSettleParam.GetTradeId(),
		"orderid":       accountSettleParam.GetOrderID(),
		"uid":           userAsset.UID,
		"openclose":     openclose,
		"pos_side":      accountSettleParam.AccountSettle.Order.PosSide.String(),
		"buy_sell":      accountSettleParam.AccountSettle.Order.Side.String(),
		"price":         accountSettleParam.GetPrice(),
		"amount":        accountSettleParam.GetAmount(),
		"margin_mode":   accountSettleParam.AccountSettle.Order.MarginMode.String(),
		"position_mode": userAsset.PositionMode.String(),
	}).Info("tp0803 trade info")

	// 根据持仓模式处理结算
	if userAsset.PositionMode == futuresPB.PositionMode_POSITION_MODE_HEDGE {
		// 双向Hedge持仓结算逻辑
		err := use.settleSinglePosition(userAsset, accountSettleParam, changelog)
		if err != nil {
			return err
		}
	} else {
		// 单向Both持仓结算逻辑
		err := use.settleBothPositions(userAsset, accountSettleParam)
		if err != nil {
			return err
		}
	}

	return nil
}

func (use *SettleUseCase) settleSinglePosition(userAsset *repository.AssetSwap, accountSettleParam utils.AccountSettleParam, changelog *[]string) error {

	side := accountSettleParam.GetSide()
	isOpen := accountSettleParam.IsOpenPosition()

	var err error
	if side == domain.Sell && isOpen {
		err = use.positionUC.OpenShortPos(userAsset, use.priceRepo, accountSettleParam, changelog)
	} else if side == domain.Sell && !isOpen {
		err = use.positionUC.CloseLongPos(userAsset, use.priceRepo, accountSettleParam, changelog)
	} else if side == domain.Buy && isOpen {
		err = use.positionUC.OpenLongPos(userAsset, use.priceRepo, accountSettleParam, changelog)
	} else if side == domain.Buy && !isOpen {
		err = use.positionUC.CloseShortPos(userAsset, use.priceRepo, accountSettleParam, changelog)
	}

	return err
}

func (use *SettleUseCase) settleBothPositions(userAsset *repository.AssetSwap, accountSettleParam utils.AccountSettleParam) error {

	// 体验金?
	// if isTrial {
	// 	// 体验金不支持单向持仓
	// 	err = fmt.Errorf("trial not support both pos")
	// 	return reply, domain.Code251026, err
	// }

	side := accountSettleParam.GetSide()
	isOpen := accountSettleParam.IsOpenPosition()
	amount := accountSettleParam.GetAmount()

	if side == domain.Sell {
		amount = amount.Neg() // 空仓置为负数?
	}

	if (side == domain.Buy && isOpen && userAsset.BothPos.Pos.Sign() >= 0) ||
		(side == domain.Sell && isOpen && userAsset.BothPos.Pos.Sign() <= 0) { // Brad说单向模式负数代表空仓
		// 方向一致直接开仓
		err := use.positionUC.OpenBothPos(userAsset, use.priceRepo, accountSettleParam)
		if err != nil {
			return err
		}
	} else {
		// price := accountSettleParam.GetPrice()
		// feeRate := accountSettleParam.GetFeeRate()
		// closeAmount := amount.Abs()
		// openAmount := decimal.Zero
		// closeFee := closeAmount.Mul(price).Mul(feeRate).Truncate(domain.CurrencyPrecision)
		// cUnfrozenMargin := accountSettleParam.GetUnfrozenMargin()

		// if userAsset.BothPos.Pos.Abs().LessThan(tradeAmount.Abs()) {
		// 	closeAmount = bothPos.Pos.Neg()
		// 	closeFee = trade.Fee.Mul(closeAmount.Abs().Div(req.Amount)).Abs().Truncate(domain.CurrencyPrecision)

		// 	cUnfrozenMargin = trade.UnfrozenMargin.Mul(closeAmount.Abs().Div(req.Amount)).Abs().Truncate(domain.CurrencyPrecision)
		// 	openAmount = bothPos.Pos.Add(tradeAmount)
		// }

		// 反向对冲平仓?
		err := use.positionUC.CloseBothPos(userAsset, use.priceRepo, accountSettleParam)
		if err != nil {
			return err
		}
		// Todo 再开仓
	}

	return nil
}

func (use *SettleUseCase) saveUserAsset(ctx context.Context, userAsset *repository.AssetSwap, contractCode string) error {
	if userAsset == nil {
		return fmt.Errorf("user asset is nil")
	}
	var data []*commonpb.KafkaFundingChange

	for currency, balance := range userAsset.Balance {
		data = append(data, &commonpb.KafkaFundingChange{
			Uid:        userAsset.UID,
			Currency:   currency,
			ChangeType: commonpb.FundingChangeType_FundingChangeType_Balance,
			Value:      balance.String(),
		})
	}
	for currency, frozen := range userAsset.Frozen {
		data = append(data, &commonpb.KafkaFundingChange{
			Uid:        userAsset.UID,
			Currency:   currency,
			ChangeType: commonpb.FundingChangeType_FundingChangeType_Frozen,
			Value:      frozen.String(),
		})
	}

	// 将资产余额 发送到kafka，供后续更新
	err := use.producerUC.SendFundingChange(data)
	if err != nil {
		logrus.WithFields(logrus.Fields{
			"uid":          userAsset.UID,
			"contractCode": contractCode,
			"PositionMode": userAsset.PositionMode.String(),
			"AssetMode":    userAsset.AssetMode.String(),
			"Balance":      userAsset.Balance,
			"Frozen":       userAsset.Frozen,
			"LongPos":      userAsset.LongPos,
			"ShortPos":     userAsset.ShortPos,
			"BothPos":      userAsset.BothPos,
			"error":        err,
		}).Error("SettleUseCase.saveUserAsset")
	}
	logrus.Infof("saveUserAsset: uid=%s, contractCode=%s", userAsset.UID, contractCode)

	logrus.Info("saveUserAsset: asset saved successfully (placeholder implementation)")
	return nil
}
