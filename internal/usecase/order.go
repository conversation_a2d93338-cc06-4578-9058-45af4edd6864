package usecase

import (
	"context"
	"fmt"
	"futures-asset/configs"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"
	"futures-asset/internal/libs/pager"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
	cfg "yt.com/backend/common.git/config"
)

// OrderUseCaseParam 资产用例参数
type OrderUseCaseParam struct {
	dig.In

	Config    *cfg.Config[configs.Config] `name:"config"`
	OrderRepo repository.OrderRepository
}

// OrderUseCase 资产用例实现
type OrderUseCase struct {
	config       *cfg.Config[configs.Config]
	orderRepo    repository.OrderRepository
	assetUseCase usecase.AssetUseCase
}

// NewOrderUseCase 创建实例
func NewOrderUseCase(param OrderUseCaseParam) usecase.OrderUseCase {
	return &OrderUseCase{
		config:    param.Config,
		orderRepo: param.OrderRepo,
	}
}

// OrderList implements usecase.OrderUseCase.
func (use *OrderUseCase) OrderList(ctx context.Context, req *usecase.OrderListParam) (usecase.OrderListResponse, error) {
	if req == nil {
		return usecase.OrderListResponse{}, nil
	}
	param := repository.OrderPageParam{
		Condition: req.Condition,
		UID:       req.UID,
		Symbol:    req.Symbol,
		OrderID:   req.OrderID,
	}
	resp, total, err := use.orderRepo.Page(ctx, param)
	if err != nil {
		logrus.Error(fmt.Sprintf("orderRepo.Page err:%s", err))
		return usecase.OrderListResponse{}, err
	}

	return usecase.OrderListResponse{
		Orders: resp,
		Page: pager.Page{
			Total:     total,
			PageSize:  req.PageSize,
			PageIndex: req.PageIndex,
		},
	}, nil
}
