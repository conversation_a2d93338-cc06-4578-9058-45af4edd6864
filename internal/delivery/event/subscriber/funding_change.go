package subscriber

import (
	"context"
	"encoding/json"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"github.com/IBM/sarama"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
	commonpb "yt.com/backend/common.git/business/grpc/gen/ws/v1"
)

type FundingChangeSubscriberParam struct {
	dig.In

	assetRepository repository.AssetRepository
}

type FundingChangeSubscriber struct {
	assetRepository repository.AssetRepository
}

func NewFundingChangeHandler(change FundingChangeSubscriberParam) *FundingChangeSubscriber {
	return &FundingChangeSubscriber{assetRepository: change.assetRepository}
}

func (handle *FundingChangeSubscriber) Handle(session sarama.ConsumerGroupSession,
	claim sarama.ConsumerGroupClaim, msg *sarama.ConsumerMessage) {

	defer session.MarkMessage(msg, "")

	var events []*commonpb.KafkaFundingChange
	err := json.Unmarshal(msg.Value, &events)
	if err != nil {
		field := genLogField(msg, err, "json.Unmarshal")
		logrus.WithFields(field).Error("FundingChangeSubscriber.Handle")
		return
	}
	var balanceDecimal decimal.Decimal
	for _, event := range events {
		if event == nil || event.Uid == "" || event.Currency == "" {
			field := genLogField(msg, err, "event.id is zero")
			logrus.WithFields(field).Error("FundingChangeSubscriber.Handle")
			continue
		}

		balanceDecimal, err = decimal.NewFromString(event.Value)
		if err != nil {
			field := genLogField(msg, err, "decimal.NewFromString Balance failed")
			logrus.WithFields(field).Error("FundingChangeSubscriber.Handle")
			continue
		}

		up := entity.Wallet{
			UID:        event.Uid,
			Currency:   event.Currency,
			Balance:    balanceDecimal,
			Frozen:     event.Value,
			UpdateTime: event.FundingTime,
		}

		err = handle.assetRepository.UpdateAsset(context.Background(), up, event.ChangeType)
		if err != nil {
			field := genLogField(msg, err, "update failed")
			logrus.WithFields(field).Error("FundingChangeSubscriber.Handle")
		}
	}
}

func genLogField(msg *sarama.ConsumerMessage, err error, s string) logrus.Fields {
	return logrus.Fields{
		"topic":     msg.Topic,
		"key":       msg.Key,
		"partition": msg.Partition,
		"offset":    msg.Offset,
		"msg":       string(msg.Value),
		"err":       err,
		"type":      s,
	}
}
