package event

import (
	"context"
	"fmt"

	"futures-asset/internal/domain"

	"github.com/IBM/sarama"
	"github.com/sirupsen/logrus"

	"futures-asset/handler"
	"futures-asset/internal/delivery/event/subscriber"

	"yt.com/backend/common.git/kafka"
)

type SubscriberHandlerFunc func(
	session sarama.ConsumerGroupSession,
	claim sarama.ConsumerGroupClaim,
	msg *sarama.ConsumerMessage,
)

func (s *Server) registerSubscriberHandler(ctx context.Context) error {
	err := s.container.Invoke(
		func(
			bill subscriber.BillSubscriberParam,
			rate subscriber.FundingRateSubscriberParam,
			fee subscriber.FundingFeeSubscriberParam,
			change subscriber.FundingChangeSubscriberParam,
			messageProcessor *handler.MessageProcessor,
		) error {
			billHandler := subscriber.NewBillHandler(bill)
			rateHandler := subscriber.NewFundingRateHandler(rate)
			feeHandler := subscriber.NewFundingFeeHandler(fee)
			changeHandler := subscriber.NewFundingChangeHandler(change)

			consumerHandle := map[string]SubscriberHandlerFunc{
				domain.SettleTradeAccountTopic: messageProcessor.MessageQueueHandler,
				domain.SettleCancelOrderTopic:  messageProcessor.MessageQueueHandler,
				domain.AssetsBillTopic:         billHandler.Handle,
				domain.FundingRateTopic:        rateHandler.Handle,
				domain.FundingFeeTopic:         feeHandler.Handle,
				domain.FundingChangeTopic:      changeHandler.Handle,
			}
			for topic, handle := range consumerHandle {
				go func(t string, h SubscriberHandlerFunc) {
					s.subscribeTopic(ctx, t, h)
				}(topic, handle)
			}

			return nil
		})
	if err != nil {
		return fmt.Errorf("register subscriber handler failed: %w", err)
	}

	return nil
}

func (s *Server) subscribeTopic(ctx context.Context, topic string, handler SubscriberHandlerFunc) {
	kc := kafka.NewDefaultConsumerConfig()

	// 不採用自動提交
	kc.Offsets.AutoCommit.Enable = true
	kc.GroupID = s.conf.Kafka.Consumer.GroupID

	// 一個ConsumerGroup client只能處理一個Consume，有多個所以必須每個ConsumerGroup就new一個client
	cli, err := kafka.NewConsumerGroup(ctx, &kafka.Config{
		ClientID:          s.conf.Kafka.ClientID,
		Brokers:           s.conf.Kafka.Brokers,
		ChannelBufferSize: s.conf.Kafka.ChannelBufferSize,
		SASL: kafka.SASL{
			Enable:    s.conf.Kafka.SASL.Enable,
			Mechanism: sarama.SASLMechanism(s.conf.Kafka.SASL.Mechanism),
			User:      s.conf.Kafka.SASL.User,
			Password:  s.conf.Kafka.SASL.Password,
		},
	}, kc)

	if err != nil {
		panic(fmt.Errorf("kafka.NewConsumerUseCase error: %w", err))
	}

	defer func() {
		if err := cli.Close(); err != nil {
			logrus.WithError(err).Error("failed to close consumer group")
		}
	}()

	s.subscribers[topic] = cli

	logrus.WithContext(ctx).WithFields(logrus.Fields{
		"topic":       s.conf.Kafka.Consumer.Topic[topic],
		"topic_group": topic,
	}).Info("start ConsumerGroup topic")

	if err = cli.Consume(ctx, &kafka.ConsumerGroupHandler{
		Topics:         s.conf.Kafka.Consumer.Topic[topic],
		ExitHandle:     s.ExitHandle,
		MessagesHandle: handler,
	}); err != nil {
		logrus.WithFields(logrus.Fields{
			"err":         err,
			"topic":       s.conf.Kafka.Consumer.Topic[topic],
			"topic_group": topic,
		}).Error("start ConsumerGroup topic")
	}
}

// ExitHandle 關閉ConsumerGroup時觸發
func (s *Server) ExitHandle(_ sarama.ConsumerGroupSession, _ sarama.ConsumerGroupClaim, source string) error {
	logrus.WithFields(logrus.Fields{
		"topic":  s.conf.Kafka.Consumer.Topic,
		"id":     s.conf.Kafka.Consumer.GroupID,
		"source": source,
	}).Info("kafka ConsumerUseCase close")

	return nil
}
